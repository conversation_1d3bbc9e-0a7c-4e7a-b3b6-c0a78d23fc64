import { But<PERSON> } from "@/components/ui/button";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Pagination, PaginationContent, PaginationItem, PaginationPrevious, PaginationLink, PaginationEllipsis, PaginationNext } from "@/components/ui/pagination";
import { UserCard } from "@/components/user-card";
import { SearchIcon } from "lucide-react";

export default function Page() {
  return (
    <div className="w-full flex flex-col gap-4 h-full">
      <div className="w-full flex gap-4">
        <Input
          type="text"
          placeholder="Pesquisar..."
          className="w-full"
        />
        <Button size="icon">
          <SearchIcon />
        </Button>
      </div>
      <div className="flex flex-col gap-4 h-full">
        <UserCard/>
      </div>  
      <div>
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" />
            </PaginationItem>
            <PaginationItem>
              <PaginationLink href="#">1</PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}
