"use client"
import { <PERSON><PERSON>, Avatar<PERSON>allback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Pagination, PaginationContent, PaginationItem, PaginationPrevious, PaginationLink, PaginationEllipsis, PaginationNext } from "@/components/ui/pagination"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ProfileCard, type Profile } from "./_components/profile-card"
import {
  CheckCircle,
  Phone,
  Mail,
  Calendar,
  Edit,
  Star,
  MessageSquare,
  ThumbsUp,
  <PERSON>ly,
  User,
  Plus,
  ChevronDown,
} from "lucide-react"
import { useState } from "react"

export default function ProfilePage() {
  // View selection state
  const [selectedView, setSelectedView] = useState<'profiles' | 'reviews' | 'replies'>('profiles')

  // Pagination state
  const [currentProfilesPage, setCurrentProfilesPage] = useState(1)
  const [currentReviewsPage, setCurrentReviewsPage] = useState(1)
  const [currentRepliesPage, setCurrentRepliesPage] = useState(1)
  const itemsPerPage = 4

  // This would typically come from your authentication system or API
  const user = {
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    avatarUrl: "/placeholder.svg?height=128&width=128",
    isVerified: true,
    role: "Administrator",
    initials: "SJ",
    joinDate: "January 2023",
  }

  // Mock data for user profiles
  const userProfiles: Profile[] = [
    {
      id: 1,
      name: "Sarah's Coffee Shop",
      type: "Business Owner",
      description: "Local coffee shop serving artisanal coffee and pastries",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: true,
      createdDate: "March 2023",
      reviewsCount: 45,
      avgRating: 4.8,
      category: "Food & Beverage",
    },
    {
      id: 2,
      name: "Tech Reviewer Sarah",
      type: "Content Creator",
      description: "Technology product reviews and tutorials",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: true,
      createdDate: "January 2023",
      reviewsCount: 128,
      avgRating: 4.6,
      category: "Technology",
    },
    {
      id: 3,
      name: "Fitness Coach Sarah",
      type: "Service Provider",
      description: "Personal fitness training and wellness coaching",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: false,
      createdDate: "June 2023",
      reviewsCount: 23,
      avgRating: 4.9,
      category: "Health & Fitness",
    },
    {
      id: 4,
      name: "Sarah Johnson - Customer",
      type: "Personal",
      description: "Personal profile for reviews and recommendations",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: true,
      createdDate: "January 2023",
      reviewsCount: 67,
      avgRating: null,
      category: "General",
    },
  ]

  // Mock data for reviews
  const userReviews = [
    {
      id: 1,
      businessName: "The Coffee House",
      rating: 5,
      comment:
        "Amazing coffee and great atmosphere! The baristas are very friendly and knowledgeable. I especially loved their signature latte.",
      date: "2 days ago",
      likes: 12,
      replies: 3,
    },
    {
      id: 2,
      businessName: "Mario's Italian Restaurant",
      rating: 4,
      comment: "Great pasta and excellent service. The tiramisu was absolutely delicious. Will definitely come back!",
      date: "1 week ago",
      likes: 8,
      replies: 1,
    },
    {
      id: 3,
      businessName: "Tech Store Plus",
      rating: 3,
      comment: "Good selection of products but customer service could be improved. Prices are reasonable though.",
      date: "2 weeks ago",
      likes: 5,
      replies: 0,
    },
    {
      id: 4,
      businessName: "Fitness Center Pro",
      rating: 5,
      comment: "Excellent facilities and very clean. The staff is helpful and the equipment is top-notch.",
      date: "3 weeks ago",
      likes: 15,
      replies: 2,
    },
    {
      id: 5,
      businessName: "Book Haven",
      rating: 4,
      comment: "Great selection of books and cozy reading areas. The staff recommendations are always spot on.",
      date: "1 month ago",
      likes: 9,
      replies: 1,
    },
  ]

  // Mock data for review replies
  const userReplies = [
    {
      id: 1,
      originalReview: "Great service at Downtown Cafe!",
      originalReviewer: "John D.",
      businessName: "Downtown Cafe",
      reply: "Thank you for the kind words! We're glad you enjoyed your experience. Hope to see you again soon!",
      date: "3 days ago",
      likes: 6,
    },
    {
      id: 2,
      originalReview: "The food was okay but service was slow...",
      originalReviewer: "Mike R.",
      businessName: "Burger Palace",
      reply:
        "We apologize for the slow service. We've been working on improving our wait times. Please give us another chance!",
      date: "1 week ago",
      likes: 4,
    },
    {
      id: 3,
      originalReview: "Love the new menu items!",
      originalReviewer: "Lisa K.",
      businessName: "Pasta Corner",
      reply: "Thank you! We're always working to bring fresh and exciting dishes to our customers.",
      date: "2 weeks ago",
      likes: 8,
    },
  ]

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`h-4 w-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} />
    ))
  }



  // Pagination logic
  const getPaginatedData = (data: any[], currentPage: number) => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return data.slice(startIndex, endIndex)
  }

  const getTotalPages = (dataLength: number) => {
    return Math.ceil(dataLength / itemsPerPage)
  }

  const generatePageNumbers = (currentPage: number, totalPages: number) => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) {
          pages.push(i)
        }
        pages.push('ellipsis')
        pages.push(totalPages)
      } else if (currentPage >= totalPages - 2) {
        pages.push(1)
        pages.push('ellipsis')
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        pages.push(1)
        pages.push('ellipsis')
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i)
        }
        pages.push('ellipsis')
        pages.push(totalPages)
      }
    }

    return pages
  }

  // Get paginated data for each tab
  const paginatedProfiles = getPaginatedData(userProfiles, currentProfilesPage)
  const paginatedReviews = getPaginatedData(userReviews, currentReviewsPage)
  const paginatedReplies = getPaginatedData(userReplies, currentRepliesPage)

  const totalProfilesPages = getTotalPages(userProfiles.length)
  const totalReviewsPages = getTotalPages(userReviews.length)
  const totalRepliesPages = getTotalPages(userReplies.length)

  // Handler for profile view action
  const handleProfileView = (profile: Profile) => {
    console.log('Viewing profile:', profile)
    // Add your profile view logic here
  }

  // Helper function to get view details
  const getViewDetails = (view: 'profiles' | 'reviews' | 'replies') => {
    switch (view) {
      case 'profiles':
        return {
          icon: <User className="h-4 w-4" />,
          label: `Profiles (${userProfiles.length})`,
          description: 'Manage your different profiles across the platform'
        }
      case 'reviews':
        return {
          icon: <Star className="h-4 w-4" />,
          label: `Reviews made by user (${userReviews.length})`,
          description: 'Reviews you have written'
        }
      case 'replies':
        return {
          icon: <Reply className="h-4 w-4" />,
          label: `Replies (${userReplies.length})`,
          description: 'Your replies to reviews'
        }
    }
  }

  return (
    <div className="min-h-screen bg-muted/30">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 p-6 h-full">
        {/* Profile Sidebar */}
        <div className="lg:col-span-1">
          <Card className="sticky top-6">
            <div className="bg-gradient-to-r from-muted/80 to-muted h-32 relative">
              <div className="absolute -bottom-12 left-1/2 -translate-x-1/2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Avatar className="h-24 w-24 border-4 border-background shadow-md">
                        <AvatarImage src={user.avatarUrl || "/placeholder.svg"} alt={user.name} />
                        <AvatarFallback>{user.initials}</AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Profile picture</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <CardHeader className="pt-16 pb-4">
              <div className="space-y-1.5 text-center">
                <div className="flex items-center justify-center gap-2">
                  <h2 className="text-xl font-bold">{user.name}</h2>
                  {user.isVerified && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <CheckCircle className="h-5 w-5 text-primary" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Verified user</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="pb-6">
              <Separator className="my-4" />
              <div className="space-y-4">
                <div className="grid gap-3">
                  <div className="flex items-center gap-3 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <div className="flex flex-col">
                      <span className="font-medium">Email</span>
                      <span className="text-xs text-muted-foreground break-all">{user.email}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div className="flex flex-col">
                      <span className="font-medium">Phone</span>
                      <span className="text-xs text-muted-foreground">{user.phone}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div className="flex flex-col">
                      <span className="font-medium">Joined</span>
                      <span className="text-xs text-muted-foreground">{user.joinDate}</span>
                    </div>
                  </div>
                </div>
              </div>
              <Separator className="my-4" />
              <Button className="w-full" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-3">
          <Card className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-semibold">Activity Dashboard</h3>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="gap-1">
                    <User className="h-3 w-3" />
                    {userProfiles.length} Profiles
                  </Badge>
                  <Badge variant="secondary" className="gap-1">
                    <Star className="h-3 w-3" />
                    {userReviews.length} Reviews
                  </Badge>
                  <Badge variant="secondary" className="gap-1">
                    <Reply className="h-3 w-3" />
                    {userReplies.length} Replies
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="h-full">
              <div className="w-full h-full flex flex-col">
                {/* Dropdown for view selection */}
                <div className="mb-6">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        <div className="flex items-center gap-2">
                          {getViewDetails(selectedView).icon}
                          {getViewDetails(selectedView).label}
                        </div>
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full">
                      <DropdownMenuItem
                        onClick={() => setSelectedView('profiles')}
                        className="flex items-center gap-2"
                      >
                        <User className="h-4 w-4" />
                        Profiles ({userProfiles.length})
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setSelectedView('reviews')}
                        className="flex items-center gap-2"
                      >
                        <Star className="h-4 w-4" />
                        Reviews made by user ({userReviews.length})
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setSelectedView('replies')}
                        className="flex items-center gap-2"
                      >
                        <Reply className="h-4 w-4" />
                        Replies ({userReplies.length})
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Conditional content rendering */}
                <div className="flex-1 h-full">
                  {selectedView === 'profiles' && (
                    <div className="space-y-4 h-full flex flex-col">
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground">
                          {getViewDetails('profiles').description}
                        </p>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Profile
                        </Button>
                      </div>
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 flex-1 overflow-y-auto pr-2">
                      {paginatedProfiles.map((profile) => (
                        <ProfileCard
                          key={profile.id}
                          profile={profile}
                          onView={handleProfileView}
                        />
                      ))}
                    </div>
                    {totalProfilesPages > 1 && (
                      <div className="mt-4">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault()
                                  if (currentProfilesPage > 1) {
                                    setCurrentProfilesPage(currentProfilesPage - 1)
                                  }
                                }}
                                className={currentProfilesPage === 1 ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                            {generatePageNumbers(currentProfilesPage, totalProfilesPages).map((page, index) => (
                              <PaginationItem key={index}>
                                {page === 'ellipsis' ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <PaginationLink
                                    href="#"
                                    onClick={(e) => {
                                      e.preventDefault()
                                      setCurrentProfilesPage(page as number)
                                    }}
                                    isActive={currentProfilesPage === page}
                                  >
                                    {page}
                                  </PaginationLink>
                                )}
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault()
                                  if (currentProfilesPage < totalProfilesPages) {
                                    setCurrentProfilesPage(currentProfilesPage + 1)
                                  }
                                }}
                                className={currentProfilesPage === totalProfilesPages ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                    </div>
                  )}

                  {selectedView === 'reviews' && (
                    <div className="h-full flex flex-col space-y-4">
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 flex-1 overflow-y-auto pr-2">
                      {paginatedReviews.map((review) => (
                      <Card key={review.id} className="border-l-4 border-l-primary/20 h-fit">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold">{review.businessName}</h4>
                              <div className="flex items-center gap-2 mt-1">
                                <div className="flex">{renderStars(review.rating)}</div>
                                <span className="text-sm text-muted-foreground">{review.date}</span>
                              </div>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-sm leading-relaxed">{review.comment}</p>
                        </CardContent>
                        <CardFooter className="pt-0 flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-4 w-4" />
                            {review.likes}
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-4 w-4" />
                            {review.replies} replies
                          </div>
                        </CardFooter>
                      </Card>
                    ))}
                    </div>
                    {totalReviewsPages > 1 && (
                      <div className="mt-4">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault()
                                  if (currentReviewsPage > 1) {
                                    setCurrentReviewsPage(currentReviewsPage - 1)
                                  }
                                }}
                                className={currentReviewsPage === 1 ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                            {generatePageNumbers(currentReviewsPage, totalReviewsPages).map((page, index) => (
                              <PaginationItem key={index}>
                                {page === 'ellipsis' ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <PaginationLink
                                    href="#"
                                    onClick={(e) => {
                                      e.preventDefault()
                                      setCurrentReviewsPage(page as number)
                                    }}
                                    isActive={currentReviewsPage === page}
                                  >
                                    {page}
                                  </PaginationLink>
                                )}
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault()
                                  if (currentReviewsPage < totalReviewsPages) {
                                    setCurrentReviewsPage(currentReviewsPage + 1)
                                  }
                                }}
                                className={currentReviewsPage === totalReviewsPages ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                    </div>
                  )}

                  {selectedView === 'replies' && (
                    <div className="h-full flex flex-col space-y-4">
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 flex-1 overflow-y-auto pr-2">
                      {paginatedReplies.map((reply) => (
                      <Card key={reply.id} className="border-l-4 border-l-blue-200 h-fit">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold">{reply.businessName}</h4>
                              <p className="text-sm text-muted-foreground mt-1">
                                Replying to <span className="font-medium">{reply.originalReviewer}</span> • {reply.date}
                              </p>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0 space-y-3">
                          <div className="bg-muted/50 p-3 rounded-md border-l-2 border-muted-foreground/20">
                            <p className="text-sm italic">"{reply.originalReview}"</p>
                          </div>
                          <p className="text-sm leading-relaxed">{reply.reply}</p>
                        </CardContent>
                        <CardFooter className="pt-0 flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-4 w-4" />
                            {reply.likes}
                          </div>
                        </CardFooter>
                      </Card>
                    ))}
                    </div>
                    {totalRepliesPages > 1 && (
                      <div className="mt-4">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault()
                                  if (currentRepliesPage > 1) {
                                    setCurrentRepliesPage(currentRepliesPage - 1)
                                  }
                                }}
                                className={currentRepliesPage === 1 ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                            {generatePageNumbers(currentRepliesPage, totalRepliesPages).map((page, index) => (
                              <PaginationItem key={index}>
                                {page === 'ellipsis' ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <PaginationLink
                                    href="#"
                                    onClick={(e) => {
                                      e.preventDefault()
                                      setCurrentRepliesPage(page as number)
                                    }}
                                    isActive={currentRepliesPage === page}
                                  >
                                    {page}
                                  </PaginationLink>
                                )}
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault()
                                  if (currentRepliesPage < totalRepliesPages) {
                                    setCurrentRepliesPage(currentRepliesPage + 1)
                                  }
                                }}
                                className={currentRepliesPage === totalRepliesPages ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
