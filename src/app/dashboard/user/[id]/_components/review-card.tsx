import { Button } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader } from "@/components/ui/card"
import { Edit, Star, ThumbsUp, MessageSquare } from "lucide-react"

export interface Review {
  id: number
  businessName: string
  rating: number
  comment: string
  date: string
  likes: number
  replies: number
}

interface ReviewCardProps {
  review: Review
  onEdit?: (review: Review) => void
}

const renderStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, i) => (
    <Star key={i} className={`h-4 w-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} />
  ))
}

export function ReviewCard({ review, onEdit }: ReviewCardProps) {
  const handleEditClick = () => {
    if (onEdit) {
      onEdit(review)
    }
  }

  return (
    <Card className="border-l-4 border-l-primary/20 h-fit">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <h4 className="font-semibold">{review.businessName}</h4>
            <div className="flex items-center gap-2 mt-1">
              <div className="flex">{renderStars(review.rating)}</div>
              <span className="text-sm text-muted-foreground">{review.date}</span>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={handleEditClick}>
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm leading-relaxed">{review.comment}</p>
      </CardContent>
      <CardFooter className="pt-0 flex items-center gap-4 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <ThumbsUp className="h-4 w-4" />
          {review.likes}
        </div>
        <div className="flex items-center gap-1">
          <MessageSquare className="h-4 w-4" />
          {review.replies} replies
        </div>
      </CardFooter>
    </Card>
  )
}
