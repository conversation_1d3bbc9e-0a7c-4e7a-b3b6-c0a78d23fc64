import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Building2, Store, Users, User, Eye } from "lucide-react"

export interface Profile {
  id: number
  name: string
  type: "Business Owner" | "Content Creator" | "Service Provider" | "Personal"
  description: string
  avatar: string
  isActive: boolean
  createdDate: string
  reviewsCount: number
  avgRating: number | null
  category: string
}

interface ProfileCardProps {
  profile: Profile
  onView?: (profile: Profile) => void
}

const getProfileIcon = (type: string) => {
  switch (type) {
    case "Business Owner":
      return <Building2 className="h-5 w-5" />
    case "Content Creator":
      return <Users className="h-5 w-5" />
    case "Service Provider":
      return <Store className="h-5 w-5" />
    case "Personal":
      return <User className="h-5 w-5" />
    default:
      return <User className="h-5 w-5" />
  }
}

const getProfileTypeColor = (type: string) => {
  switch (type) {
    case "Business Owner":
      return "bg-blue-100 text-blue-800 border-blue-200"
    case "Content Creator":
      return "bg-purple-100 text-purple-800 border-purple-200"
    case "Service Provider":
      return "bg-green-100 text-green-800 border-green-200"
    case "Personal":
      return "bg-gray-100 text-gray-800 border-gray-200"
    default:
      return "bg-gray-100 text-gray-800 border-gray-200"
  }
}

export function ProfileCard({ profile, onView }: ProfileCardProps) {
  const handleViewClick = () => {
    if (onView) {
      onView(profile)
    }
  }

  return (
    <Card className="border-l-4 border-l-primary/20">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={profile.avatar || "/placeholder.svg"} alt={profile.name} />
              <AvatarFallback>{getProfileIcon(profile.type)}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h4 className="font-semibold">{profile.name}</h4>
                {!profile.isActive && (
                  <Badge variant="outline" className="text-xs">
                    Inactive
                  </Badge>
                )}
              </div>
              <Badge
                variant="outline"
                className={`text-xs mt-1 ${getProfileTypeColor(profile.type)}`}
              >
                {profile.type}
              </Badge>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={handleViewClick}>
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-3">{profile.description}</p>
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          <span>Created {profile.createdDate}</span>
        </div>
      </CardContent>
    </Card>
  )
}
