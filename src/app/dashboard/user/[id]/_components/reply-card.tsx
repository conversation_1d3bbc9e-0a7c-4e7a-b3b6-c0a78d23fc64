import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ead<PERSON> } from "@/components/ui/card"
import { Edit, ThumbsUp } from "lucide-react"

export interface Reply {
  id: number
  originalReview: string
  originalReviewer: string
  businessName: string
  reply: string
  date: string
  likes: number
}

interface ReplyCardProps {
  reply: Reply
  onEdit?: (reply: Reply) => void
}

export function ReplyCard({ reply, onEdit }: ReplyCardProps) {
  const handleEditClick = () => {
    if (onEdit) {
      onEdit(reply)
    }
  }

  return (
    <Card className="border-l-4 border-l-blue-200 h-fit">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <h4 className="font-semibold">{reply.businessName}</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Replying to <span className="font-medium">{reply.originalReviewer}</span> • {reply.date}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={handleEditClick}>
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0 space-y-3">
        <div className="bg-muted/50 p-3 rounded-md border-l-2 border-muted-foreground/20">
          <p className="text-sm italic">"{reply.originalReview}"</p>
        </div>
        <p className="text-sm leading-relaxed">{reply.reply}</p>
      </CardContent>
      <CardFooter className="pt-0 flex items-center gap-4 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <ThumbsUp className="h-4 w-4" />
          {reply.likes}
        </div>
      </CardFooter>
    </Card>
  )
}
