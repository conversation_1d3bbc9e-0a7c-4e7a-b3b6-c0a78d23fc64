import { BreadCrumbNames } from "@/data/breadcrumb-names";

export interface BreadcrumbItem {
  name: string;
  href: string;
  isCurrentPage: boolean;
}

/**
 * Generates breadcrumb items based on the current pathname and breadcrumb names configuration
 * @param pathname - The current pathname (e.g., "/dashboard/users")
 * @returns Array of breadcrumb items
 */
export function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [];
  
  // Remove trailing slash and split path into segments
  const cleanPath = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname;
  const segments = cleanPath.split('/').filter(Boolean);
  
  // Build breadcrumbs progressively
  let currentPath = '';
  
  for (let i = 0; i < segments.length; i++) {
    currentPath += `/${segments[i]}`;
    const isCurrentPage = i === segments.length - 1;
    
    // Look up the name for this path segment
    const name = getBreadcrumbName(currentPath, segments.slice(i));
    
    breadcrumbs.push({
      name,
      href: currentPath,
      isCurrentPage
    });
  }
  
  return breadcrumbs;
}

/**
 * Gets the display name for a path segment from the breadcrumb names configuration
 * @param fullPath - The full path up to this segment (e.g., "/dashboard")
 * @param remainingSegments - The remaining path segments after this one
 * @returns The display name for this breadcrumb
 */
function getBreadcrumbName(fullPath: string, remainingSegments: string[]): string {
  // Add trailing slash to match the keys in BreadCrumbNames
  const pathKey = fullPath + '/';
  
  // Check if we have a direct match in the configuration
  if (BreadCrumbNames[pathKey]) {
    const config = BreadCrumbNames[pathKey];
    
    // If this is the final segment, return the main name
    if (remainingSegments.length === 0) {
      return config.name;
    }
    
    // If there are more segments, look in subroutes
    const nextSegment = remainingSegments[0];
    if (config.subroutes && config.subroutes[nextSegment]) {
      const subrouteValue = config.subroutes[nextSegment];
      
      // If it's a string, return it directly
      if (typeof subrouteValue === 'string') {
        return subrouteValue;
      }
      
      // If it's a nested configuration, return its name
      if (typeof subrouteValue === 'object' && subrouteValue.name) {
        return subrouteValue.name;
      }
    }
    
    // If no subroute match, return the main name
    return config.name;
  }
  
  // Fallback: capitalize the segment name
  const segment = fullPath.split('/').pop() || '';
  return segment.charAt(0).toUpperCase() + segment.slice(1);
}
