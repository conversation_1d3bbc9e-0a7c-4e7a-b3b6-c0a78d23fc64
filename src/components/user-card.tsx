import { CheckIcon } from "lucide-react";
import { <PERSON><PERSON>, AvatarFallback } from "./ui/avatar";

export function UserCard() {
    return (
        <div className="w-full bg-muted-foreground/10 rounded-md p-4 transition-all hover:bg-muted-foreground/20 hover:cursor-pointer flex items-center gap-4">
            <Avatar className="h-10 w-10 aspect-square">
                <AvatarFallback>FL</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
                <div className="flex items-center gap-1">
                    <p className="font-bold"><PERSON></p>
                    <CheckIcon className="w-3"/>
                </div>
                <p className="text-sm text-muted-foreground">@felipellira</p>
            </div>
        </div>
    )
}